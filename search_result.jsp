<%@page contentType="text/html;charset=UTF-8" import="java.sql.*" %>
    <html>

    <head>
        <title>商品信息展示</title>
        <style type="text/css">
            .style1 {
                font-size: 12px;
            }
            
            .style2 {
                color: #FF0000;
            }
        </style>
    </head>

    <body>
        <jsp:include page="search.jsp" />
        <%
        Connection conn = null;
        PreparedStatement pstmt = null;
        ResultSet rsAll = null;
        int recordCount = 0;
        int currentPage = 1;
        int pagesize = 2; // 每页显示的商品数
        int maxPage = 1;
        String strItem = request.getParameter("item");
        String strContent = request.getParameter("content");

        try {
            // 加载 MySQL JDBC 驱动
            Class.forName("com.mysql.cj.jdbc.Driver");
            // 连接 MySQL 数据库
            String strConn = "****************************************************************************";
            String strUser = "root";
            String strPassword = "root";
            conn = DriverManager.getConnection(strConn, strUser, strPassword);

            // 查询总记录数
            String countSql = "SELECT COUNT(*) FROM product";
            if (strItem != null && !strItem.isEmpty()) {
                countSql += " WHERE " + strItem.trim() + " LIKE ?";
                pstmt = conn.prepareStatement(countSql);
                pstmt.setString(1, "%" + strContent.trim() + "%");
                rsAll = pstmt.executeQuery();
                if (rsAll.next()) {
                    recordCount = rsAll.getInt(1);
                }
                rsAll.close();
                pstmt.close();
            } else {
                Statement stmt = conn.createStatement();
                rsAll = stmt.executeQuery(countSql);
                if (rsAll.next()) {
                    recordCount = rsAll.getInt(1);
                }
                rsAll.close();
                stmt.close();
            }

            maxPage = (recordCount + pagesize - 1) / pagesize;

            String strPage = request.getParameter("page");
            if (strPage != null && !strPage.isEmpty()) {
                currentPage = Integer.parseInt(strPage);
            }
            if (currentPage < 1) currentPage = 1;
            if (currentPage > maxPage) currentPage = maxPage;

            int offset = (currentPage - 1) * pagesize;
            String strSql = "SELECT p_id, p_type, p_name, p_price, p_quantity, p_time FROM product";
            if (strItem != null && !strItem.isEmpty()) {
                strSql += " WHERE " + strItem.trim() + " LIKE ?";
            }
            strSql += " ORDER BY p_id ASC LIMIT ? OFFSET ?";
            pstmt = conn.prepareStatement(strSql);
            if (strItem != null && !strItem.isEmpty()) {
                pstmt.setString(1, "%" + strContent.trim() + "%");
                pstmt.setInt(2, pagesize);
                pstmt.setInt(3, offset);
            } else {
                pstmt.setInt(1, pagesize);
                pstmt.setInt(2, offset);
            }
            rsAll = pstmt.executeQuery();

        } catch (Exception e) {
            e.printStackTrace();
        }
    %>

            <table width="80%" border="1" cellspacing="0" align="center">
                <tr>
                    <th>商品ID</th>
                    <th>商品名称</th>
                    <th>商品类型</th>
                    <th>商品价格</th>
                    <th>商品数量</th>
                    <th>商品时间</th>
                    <th>操作</th>
                </tr>
                <%
            if (rsAll != null) {
                while (rsAll.next()) {
        %>
                    <tr>
                        <td>
                            <%= rsAll.getString("p_id") %>
                        </td>
                        <td>
                            <%= rsAll.getString("p_name") %>
                        </td>
                        <td>
                            <%= rsAll.getString("p_type") %>
                        </td>
                        <td>
                            <%= rsAll.getFloat("p_price") %>
                        </td>
                        <td>
                            <%= rsAll.getInt("p_quantity") %>
                        </td>
                        <td>
                            <%= rsAll.getString("p_time") %>
                        </td>
                        <td>
                            <a href="#">修改</a>
                            <a href="#">删除</a>
                        </td>
                    </tr>
                    <%
                }
                rsAll.close();
                pstmt.close();
            } else {
        %>
                        <tr>
                            <td colspan="7" align="center">没有找到相关商品信息</td>
                        </tr>
                        <%
            }
        %>
            </table>

            <p align="center">
                跳转到:<input type="text" name="page"> &nbsp;&nbsp;&nbsp; 当前页:
                <%= currentPage %> /
                    <%= maxPage %>
                        <% if(currentPage > 1) { %>
                            &nbsp;&nbsp;&nbsp;<a href="search_result.jsp?page=1&item=<%= strItem %>&content=<%= strContent %>">首页</a> &nbsp;&nbsp;&nbsp;
                            <a href="search_result.jsp?page=<%= currentPage - 1 %>&item=<%= strItem %>&content=<%= strContent %>">上一页</a>
                            <% } %>
                                <% if(currentPage < maxPage) { %>
                                    &nbsp;&nbsp;&nbsp;<a href="search_result.jsp?page=<%= currentPage + 1 %>&item=<%= strItem %>&content=<%= strContent %>">下一页</a> &nbsp;&nbsp;&nbsp;
                                    <a href="search_result.jsp?page=<%= maxPage %>&item=<%= strItem %>&content=<%= strContent %>">末页</a>
                                    <% } %>
            </p>

            <%
        if (conn != null) {
            try {
                conn.close();
            } catch (SQLException e) {
                e.printStackTrace();
            }
        }
    %>
    </body>

    </html>