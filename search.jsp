<%@page contentType="text/html;charset=UTF-8" %>
    <html>

    <head>
        <title>商品搜索</title>
        <style type="text/css">
            .style1 {
                font-size: 12px;
            }
            
            .style2 {
                color: #FF0000;
            }
        </style>
    </head>

    <body>
        <form name="form1" onsubmit="return check()" method="post" action="search_result.jsp">
            <table width="80%" border="0" align="center" bgcolor="#0099FF">
                <tr bgcolor="#FFFFFF">
                    <th height="39" scope="row">
                        <div align="left">
                            <span style="font-weight: 400"><font size="2">搜索项目:</font></span>
                        </div>
                    </th>
                    <td>
                        <select name="item" size="1">
                        <option value="">请选择</option>
                        <option value="p_type">p_type</option>
                        <option value="p_id">p_id</option>
                        <option value="p_name">p_name</option>
                    </select>
                    </td>
                    <td>
                        <font size="2">搜索内容:</font>
                    </td>
                    <td><input type="text" name="content"></td>
                    <td><input type="submit" name="submit" value="搜索"></td>
                </tr>
            </table>
        </form>
    </body>

    </html>
    <script type="text/javascript">
        function check() {
            if (form1.content.value === "") {
                alert("请输入搜索内容!");
                form1.content.focus();
                return false;
            }
            return true;
        }
    </script>