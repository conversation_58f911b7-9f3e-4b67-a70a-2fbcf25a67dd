<%@page contentType="text/html;charset=UTF-8" import="java.sql.*" %>
    <html>

    <head>
        <title>数据库连接测试</title>
        <style>
            body {
                font-family: Arial, sans-serif;
                margin: 20px;
            }
            
            .success {
                color: green;
            }
            
            .error {
                color: red;
            }
            
            table {
                border-collapse: collapse;
                width: 100%;
                margin-top: 20px;
            }
            
            th,
            td {
                border: 1px solid #ddd;
                padding: 8px;
                text-align: left;
            }
            
            th {
                background-color: #f2f2f2;
            }
        </style>
    </head>

    <body>
        <h2>数据库连接和数据测试</h2>

        <%
    Connection conn = null;
    Statement stmt = null;
    ResultSet rs = null;
    
    try {
        // 加载 MySQL JDBC 驱动
        Class.forName("com.mysql.cj.jdbc.Driver");
        
        // 连接 MySQL 数据库
        String strConn = "****************************************************************************";
        String strUser = "root";
        String strPassword = "root";
        conn = DriverManager.getConnection(strConn, strUser, strPassword);
        
        out.println("<p class='success'>✓ 数据库连接成功！</p>");
        
        // 查询总记录数
        stmt = conn.createStatement();
        rs = stmt.executeQuery("SELECT COUNT(*) as total FROM product");
        int totalRecords = 0;
        if (rs.next()) {
            totalRecords = rs.getInt("total");
        }
        rs.close();
        
        out.println("<p class='success'>✓ product表中共有 " + totalRecords + " 条记录</p>");
        
        if (totalRecords > 0) {
            // 显示前10条记录
            rs = stmt.executeQuery("SELECT p_id, p_type, p_name, p_price, p_quantity, p_time FROM product ORDER BY p_id LIMIT 10");
            
            out.println("<h3>前10条商品数据：</h3>");
            out.println("<table>");
            out.println("<tr><th>商品ID</th><th>商品类型</th><th>商品名称</th><th>价格</th><th>数量</th><th>时间</th></tr>");
            
            while (rs.next()) {
                out.println("<tr>");
                out.println("<td>" + rs.getString("p_id") + "</td>");
                out.println("<td>" + rs.getString("p_type") + "</td>");
                out.println("<td>" + rs.getString("p_name") + "</td>");
                out.println("<td>" + rs.getFloat("p_price") + "</td>");
                out.println("<td>" + rs.getInt("p_quantity") + "</td>");
                out.println("<td>" + rs.getString("p_time") + "</td>");
                out.println("</tr>");
            }
            out.println("</table>");
            
            out.println("<p class='success'>✓ 数据显示正常，您可以访问 <a href='search_result.jsp'>search_result.jsp</a> 测试分页功能</p>");
        } else {
            out.println("<p class='error'>⚠ product表中没有数据，请先运行 init_database.sql 脚本插入测试数据</p>");
        }
        
    } catch (ClassNotFoundException e) {
        out.println("<p class='error'>✗ MySQL JDBC驱动未找到: " + e.getMessage() + "</p>");
        out.println("<p>请确保已将 mysql-connector-java.jar 放入 WEB-INF/lib 目录</p>");
    } catch (SQLException e) {
        out.println("<p class='error'>✗ 数据库连接失败: " + e.getMessage() + "</p>");
        out.println("<p>请检查：</p>");
        out.println("<ul>");
        out.println("<li>MySQL服务是否启动</li>");
        out.println("<li>数据库名称是否正确: D55DB392f5854bfe</li>");
        out.println("<li>用户名密码是否正确: root/root</li>");
        out.println("<li>端口是否正确: 3306</li>");
        out.println("</ul>");
    } catch (Exception e) {
        out.println("<p class='error'>✗ 其他错误: " + e.getMessage() + "</p>");
        e.printStackTrace();
    } finally {
        try {
            if (rs != null) rs.close();
            if (stmt != null) stmt.close();
            if (conn != null) conn.close();
        } catch (SQLException e) {
            e.printStackTrace();
        }
    }
    %>

            <hr>
            <h3>使用说明：</h3>
            <ol>
                <li>确保MySQL服务已启动</li>
                <li>在MySQL中运行 <code>init_database.sql</code> 脚本创建数据库和插入测试数据</li>
                <li>访问此页面检查数据库连接和数据</li>
                <li>访问 <a href="search_result.jsp">search_result.jsp</a> 测试分页功能</li>
            </ol>
    </body>

    </html>