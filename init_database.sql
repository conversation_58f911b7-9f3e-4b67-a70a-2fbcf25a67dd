-- 创建数据库（如果不存在）
CREATE DATABASE IF NOT EXISTS D55DB392f5854bfe CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 使用数据库
USE D55DB392f5854bfe;

-- 创建product表
CREATE TABLE IF NOT EXISTS product (
    p_id INT AUTO_INCREMENT PRIMARY KEY,
    p_type VARCHAR(50) NOT NULL COMMENT '商品类型',
    p_name VARCHAR(100) NOT NULL COMMENT '商品名称',
    p_price DECIMAL(10,2) NOT NULL COMMENT '商品价格',
    p_quantity INT NOT NULL DEFAULT 0 COMMENT '商品数量',
    p_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '商品时间'
);

-- 清空表数据（如果需要重新初始化）
TRUNCATE TABLE product;

-- 插入测试数据
INSERT INTO product (p_type, p_name, p_price, p_quantity, p_time) VALUES
('电子产品', 'iPhone 15 Pro', 8999.00, 50, '2024-01-15 10:30:00'),
('电子产品', 'Samsung Galaxy S24', 7999.00, 30, '2024-01-16 11:20:00'),
('电子产品', 'iPad Air', 4999.00, 25, '2024-01-17 09:15:00'),
('电子产品', 'MacBook Pro', 15999.00, 15, '2024-01-18 14:45:00'),
('电子产品', 'AirPods Pro', 1999.00, 100, '2024-01-19 16:30:00'),

('服装', '男士T恤', 199.00, 200, '2024-01-20 08:00:00'),
('服装', '女士连衣裙', 399.00, 150, '2024-01-21 10:15:00'),
('服装', '运动鞋', 599.00, 80, '2024-01-22 12:30:00'),
('服装', '牛仔裤', 299.00, 120, '2024-01-23 15:45:00'),
('服装', '羽绒服', 899.00, 60, '2024-01-24 17:20:00'),

('家居用品', '电饭煲', 499.00, 40, '2024-01-25 09:30:00'),
('家居用品', '吸尘器', 1299.00, 25, '2024-01-26 11:45:00'),
('家居用品', '床上四件套', 299.00, 90, '2024-01-27 13:15:00'),
('家居用品', '台灯', 199.00, 70, '2024-01-28 16:00:00'),
('家居用品', '收纳盒', 89.00, 150, '2024-01-29 18:30:00'),

('图书', 'Java编程思想', 89.00, 300, '2024-01-30 10:00:00'),
('图书', 'Spring Boot实战', 79.00, 250, '2024-01-31 12:15:00'),
('图书', 'MySQL必知必会', 69.00, 200, '2024-02-01 14:30:00'),
('图书', 'JavaScript高级程序设计', 99.00, 180, '2024-02-02 16:45:00'),
('图书', 'Python数据分析', 89.00, 220, '2024-02-03 19:00:00'),

('食品', '有机大米', 39.00, 500, '2024-02-04 08:30:00'),
('食品', '进口牛奶', 25.00, 300, '2024-02-05 10:45:00'),
('食品', '新鲜水果', 19.00, 400, '2024-02-06 12:00:00'),
('食品', '坚果礼盒', 159.00, 100, '2024-02-07 14:15:00'),
('食品', '茶叶礼盒', 299.00, 80, '2024-02-08 16:30:00');

-- 查看插入的数据
SELECT COUNT(*) as total_records FROM product;
SELECT * FROM product ORDER BY p_id LIMIT 10;
